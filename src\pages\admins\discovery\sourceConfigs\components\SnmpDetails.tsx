import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { Card, Group, Stack, ActionIcon, Flex, PasswordInput, Grid, HoverCard } from '@mantine/core';
import { IconPlus, IconMinus, IconInfoCircle } from '@tabler/icons-react';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import { SnmpAuthentication, SnmpEncryption, SnmpTypeOid, SnmpVersion } from '@common/constants/DiscoverySourceConfigEnum';
import { defaultSnmpInfo, SourceConfigSnmpInfo } from '@models/discovery/DiscoverySourceConfig';
import { enumToSelectData } from './BasicInfo';
import { KanbanInput, KanbanSelect, KanbanText, KanbanTitle, KanbanRadio } from 'kanban-design-system';
import styles from '../SourceConfig.module.scss';
import { SourceDataAction } from '@common/constants/SourceDataActionEnum';

export type SnmpDetailsRef = {
  getData: () => Promise<SourceConfigSnmpInfo>;
};

export type SnmpDetailsProps = {
  defaultData?: Partial<SourceConfigSnmpInfo>;
  action: SourceDataAction;
  snmpVersion?: string | undefined;
  index?: number;
  onRemove?: (index: number) => void;
};

export const oidTypeOptions = enumToSelectData(SnmpTypeOid);
export const versionOptions = enumToSelectData(SnmpVersion);
export const authenticationOptions = enumToSelectData(SnmpAuthentication);
export const encryptionOptions = enumToSelectData(SnmpEncryption);

const SnmpDetails = forwardRef<SnmpDetailsRef, SnmpDetailsProps>(({ action, defaultData, index, onRemove, snmpVersion: snmpVersion }, ref) => {
  const { control, handleSubmit, reset } = useForm<SourceConfigSnmpInfo>({
    defaultValues: defaultData || defaultSnmpInfo,
  });
  const isViewAction = SourceDataAction.VIEW === action;
  const { append, fields, remove, replace } = useFieldArray({
    control,
    name: 'oidsInfo',
  });

  // Sử dụng ref để check init lần đầu
  const initializedRef = useRef(false);

  // Reset form khi có dữ liệu prop từ server - edit
  useEffect(() => {
    if (defaultData) {
      // Nếu có hostName (dữ liệu từ server) hoặc chưa được khởi tạo
      if (defaultData.hostName || !initializedRef.current) {
        reset(defaultData);
        replace(defaultData.oidsInfo ?? []);
        initializedRef.current = true;
      }
    }
  }, [defaultData, reset, replace]);
  useImperativeHandle(ref, () => ({
    getData: async () => {
      return new Promise<SourceConfigSnmpInfo>((resolve) => {
        handleSubmit((data) => resolve(data))();
      });
    },
  }));
  return (
    <Card withBorder p='md' radius='md'>
      {!isViewAction && onRemove && index !== undefined && (
        <ActionIcon
          variant='filled'
          color='red'
          radius='xl'
          style={{ position: 'absolute', top: '10px', right: '10px', zIndex: 1 }}
          onClick={() => onRemove(index)}>
          <IconMinus size={16} />
        </ActionIcon>
      )}
      <KanbanTitle order={4}>SNMP Details</KanbanTitle>
      <Stack gap='md'>
        <Grid columns={24}>
          <Grid.Col span={{ base: 24, md: 12, lg: 12 }}>
            <Controller
              name='hostName'
              control={control}
              rules={{ required: 'Target IP / Hostname is required' }}
              render={({ field, fieldState }) => (
                <KanbanInput
                  label='Target IP / Hostname'
                  {...field}
                  error={fieldState.error?.message}
                  required
                  disabled={isViewAction}
                  maxLength={2000}
                />
              )}
            />
          </Grid.Col>
          <Grid.Col span={{ base: 1, md: 1, lg: 1 }}>
            <HoverCard position='right' width={280} shadow='md'>
              <HoverCard.Target>
                <Flex
                  h='100%'
                  align='center'
                  justify='flex-start'
                  mt={8} // 8px
                >
                  <IconInfoCircle color={'var(--mantine-color-blue-3)'} />
                </Flex>
              </HoverCard.Target>
              <HoverCard.Dropdown>
                <KanbanText>Separate IPs with a comma (,) or semicolon (;).Example: ********.********</KanbanText>
              </HoverCard.Dropdown>
            </HoverCard>
          </Grid.Col>
        </Grid>

        {/* Community String cho SNMPv2c */}
        {SnmpVersion.SNMPV2 === snmpVersion && (
          <Grid>
            <Grid.Col span={{ base: 12, md: 6, lg: 6 }}>
              <Controller
                name='community'
                control={control}
                rules={{ required: 'Community string is required for SNMPv2c' }}
                render={({ field, fieldState }) => (
                  <PasswordInput label='Community String' {...field} error={fieldState.error?.message} required disabled={isViewAction} />
                )}
              />
            </Grid.Col>
          </Grid>
        )}

        {/* Username/Password cho SNMPv3 */}
        {SnmpVersion.SNMPV3 === snmpVersion && (
          <>
            <Grid>
              <Grid.Col span={{ base: 12, md: 6, lg: 6 }}>
                <Controller
                  name='username'
                  rules={{ required: 'Security Name is required for SNMPv3' }}
                  control={control}
                  render={({ field, fieldState }) => (
                    <KanbanInput
                      label='Security Name'
                      {...field}
                      error={fieldState.error?.message}
                      required
                      disabled={isViewAction}
                      maxLength={2000}
                    />
                  )}
                />
              </Grid.Col>

              <Grid.Col span={{ base: 12, md: 6, lg: 6 }}>
                {/* Authentication cho SNMPv3 */}
                <Controller
                  name='authentication'
                  control={control}
                  rules={{ required: 'Authentication Protocol is required for SNMPv3' }}
                  render={({ field, fieldState }) => (
                    <KanbanRadio
                      group={{
                        name: `authentication-${index}`,
                        label: 'Authentication Protocol',
                        withAsterisk: true,
                        error: fieldState.error?.message,
                        value: field.value,
                        onChange: field.onChange,
                      }}
                      radios={authenticationOptions}
                    />
                  )}
                />
              </Grid.Col>
            </Grid>

            <Grid>
              <Grid.Col span={{ base: 12, md: 6, lg: 6 }}>
                <Controller
                  name='password'
                  control={control}
                  rules={{ required: 'Authentication Passphrase is required for SNMPv3' }}
                  render={({ field, fieldState }) => (
                    <PasswordInput
                      label='Authentication Passphrase'
                      {...field}
                      error={fieldState.error?.message}
                      required
                      disabled={isViewAction}
                      maxLength={2000}
                    />
                  )}
                />
              </Grid.Col>

              <Grid.Col span={{ base: 12, md: 6, lg: 6 }}>
                {/* Encryption cho SNMPv3 */}
                <Controller
                  name='encryption'
                  control={control}
                  rules={{ required: 'Privacy Protocol is required for SNMPv3' }}
                  render={({ field, fieldState }) => (
                    <KanbanRadio
                      group={{
                        name: `encryption-${index}`,
                        label: 'Privacy Protocol',
                        withAsterisk: true,
                        error: fieldState.error?.message,
                        value: field.value,
                        onChange: field.onChange,
                      }}
                      radios={encryptionOptions}
                    />
                  )}
                />
              </Grid.Col>
            </Grid>
            <Grid>
              <Grid.Col span={{ base: 12, md: 6, lg: 6 }}>
                <Controller
                  name='encryptionKey'
                  control={control}
                  rules={{ required: 'Privacy Passphrase is required for SNMPv3' }}
                  render={({ field, fieldState }) => (
                    <PasswordInput
                      label='Privacy Passphrase'
                      {...field}
                      error={fieldState.error?.message}
                      required
                      disabled={isViewAction}
                      maxLength={2000}
                    />
                  )}
                />
              </Grid.Col>
            </Grid>
          </>
        )}

        <Flex justify='space-between' align='center'>
          <KanbanTitle order={5}>OID Information</KanbanTitle>
          <ActionIcon variant='default' radius='xl' onClick={() => append({ type: SnmpTypeOid.SCALAR, oid: '', label: '' })} disabled={isViewAction}>
            <IconPlus size={20} />
          </ActionIcon>
        </Flex>

        {fields.map((field, index) => (
          <Group key={field.id} grow>
            <Controller
              name={`oidsInfo.${index}.type`}
              control={control}
              render={({ field }) => <KanbanSelect label='Type' data={oidTypeOptions} {...field} disabled={isViewAction} />}
            />
            <Controller
              name={`oidsInfo.${index}.oid`}
              control={control}
              render={({ field }) => <KanbanInput label='OID' {...field} disabled={isViewAction} maxLength={2000} />}
            />
            <Controller
              name={`oidsInfo.${index}.label`}
              control={control}
              render={({ field }) => <KanbanInput label='Label' {...field} disabled={isViewAction} maxLength={2000} />}
            />
            <ActionIcon disabled={isViewAction} variant='light' color='red' radius='xl' onClick={() => remove(index)} className={styles.removeButton}>
              <IconMinus size={16} />
            </ActionIcon>
          </Group>
        ))}
      </Stack>
    </Card>
  );
});
SnmpDetails.displayName = 'SnmpDetails';
export default SnmpDetails;
