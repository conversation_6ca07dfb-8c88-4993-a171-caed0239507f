import React, { forwardRef, useEffect, useImperativeHandle, useRef } from 'react';
import { Card, Stack } from '@mantine/core';
import { useForm, Controller } from 'react-hook-form';
import { DbType, IntegrateMethodEnum as Method, SnmpType, SnmpVersion, SourceDbType } from '@common/constants/DiscoverySourceConfigEnum';
import { sourceCombobox, SourceConfigBasicInfo } from '@models/discovery/DiscoverySourceConfig';
import { KanbanInput, KanbanSelect, KanbanTextarea, KanbanTitle } from 'kanban-design-system';
import { SourceDataAction } from '@common/constants/SourceDataActionEnum';
import { formatStandardName } from '@common/utils/StringUtils';

export type BasicInfoRef = {
  getData: () => Promise<SourceConfigBasicInfo | null>;
  watchMethod: Method | undefined;
};

export type BasicInfoProps = {
  action: SourceDataAction;
  defaultData?: Partial<SourceConfigBasicInfo>;
  onMethodChange?: (method: Method | undefined) => void;
  onVersionChange?: (type: string | undefined) => void;
};

export const enumToSelectData = <T extends Record<string, string>>(enumObj: T) =>
  Object.entries(enumObj).map(([key, value]) => ({
    label: key,
    value: value,
  }));

const typeOptions = {
  [Method.API]: sourceCombobox,
  [Method.DB_CONNECT]: enumToSelectData(SourceDbType),
  [Method.SNMP]: enumToSelectData(SnmpType),
};

const BasicInfo = forwardRef<BasicInfoRef, BasicInfoProps>(({ action, defaultData, onMethodChange, onVersionChange: onVersionChange }, ref) => {
  const { control, handleSubmit, reset, setValue, watch } = useForm<SourceConfigBasicInfo>({
    defaultValues: {
      name: '',
      method: undefined,
      type: undefined,
    },
  });
  const isViewAction = SourceDataAction.VIEW === action;
  const initializedRef = useRef(false);
  // init data
  useEffect(() => {
    if (defaultData && defaultData.name && !initializedRef.current) {
      // Chỉ reset khi có dữ liệu thực từ server (có name)
      reset(defaultData);
      if (onMethodChange) {
        onMethodChange(defaultData.method);
      }
      initializedRef.current = true;
    }
  }, [defaultData, onMethodChange, reset]);
  // init snmp version
  useEffect(() => {
    if (defaultData && Method.SNMP === defaultData.method && defaultData.name) {
      // Chỉ gọi onVersionChange khi có dữ liệu thực từ server (có name)
      if (onVersionChange) {
        onVersionChange(defaultData.snmpVersion ?? undefined);
      }
    }
  }, [defaultData, onVersionChange]);
  const watchMethod = watch('method');

  useImperativeHandle(ref, () => ({
    getData: async () => {
      let data: SourceConfigBasicInfo | null = null;
      await handleSubmit((d) => (data = d))();
      return data;
    },
    watchMethod,
  }));
  return (
    <Card withBorder p='md' radius='md'>
      <KanbanTitle order={4}>Basic Information</KanbanTitle>
      <Stack gap='md'>
        <Controller
          name='name'
          control={control}
          rules={{ required: 'Name is required' }}
          render={({ field, fieldState }) => (
            <KanbanInput
              label='Name'
              {...field}
              error={fieldState.error?.message}
              required
              disabled={isViewAction}
              maxLength={255}
              onChange={(event) => {
                const value = event.target.value;
                field.onChange(value);
              }}
              onBlur={(e) => {
                const value = e.target.value;
                field.onChange(formatStandardName(value));
              }}
            />
          )}
        />
        <Controller
          name='description'
          control={control}
          render={({ field }) => (
            <KanbanTextarea
              label='Description'
              maxLength={1000}
              autosize
              {...field}
              minRows={5}
              disabled={isViewAction}
              onBlur={(e) => {
                const value = e.target.value;
                field.onChange(value.trim());
              }}
            />
          )}
        />
        <Controller
          name='method'
          control={control}
          rules={{ required: 'Method is required' }}
          render={({ field, fieldState }) => (
            <KanbanSelect
              label='Method'
              disabled={isViewAction}
              data={enumToSelectData(Method)}
              {...field}
              error={fieldState.error?.message}
              required
              onChange={(value) => {
                field.onChange(value);

                if (SourceDataAction.CREATE === action) {
                  setValue('type', undefined);
                }

                if (SourceDataAction.UPDATE === action) {
                  if (value !== defaultData?.method) {
                    setValue('type', undefined);
                  } else {
                    setValue('type', defaultData?.type);
                  }
                }

                if (onMethodChange) {
                  onMethodChange(value as Method);
                }
              }}
            />
          )}
        />
        {Method.SNMP === watchMethod && (
          <Controller
            name='snmpVersion'
            disabled={isViewAction}
            control={control}
            rules={{ required: 'Version is required' }}
            render={({ field, fieldState }) => (
              <KanbanSelect
                label='Version'
                data={enumToSelectData(SnmpVersion)}
                searchable
                {...field}
                error={fieldState.error?.message}
                value={field.value}
                required
                onChange={(value) => {
                  field.onChange(value);
                  if (Method.SNMP === watchMethod && onVersionChange) {
                    onVersionChange(value ?? undefined);
                  }
                }}
              />
            )}
          />
        )}
        {Method.DB_CONNECT === watchMethod && (
          <Controller
            name='dbType'
            disabled={isViewAction}
            control={control}
            rules={{ required: 'Database Type is required' }}
            render={({ field, fieldState }) => (
              <KanbanSelect
                label='Database Type'
                data={enumToSelectData(DbType)}
                searchable
                {...field}
                error={fieldState.error?.message}
                value={field.value}
                required
                onChange={(value) => {
                  field.onChange(value);
                }}
              />
            )}
          />
        )}
        {watchMethod && (
          <Controller
            name='type'
            disabled={isViewAction}
            control={control}
            // rules={{ required: 'Type is required' }}
            render={({ field, fieldState }) => (
              <KanbanSelect
                label='Type'
                data={typeOptions[watchMethod]}
                {...field}
                searchable
                error={fieldState.error?.message}
                value={field.value}
                required
                onChange={(value) => {
                  field.onChange(value);
                }}
              />
            )}
          />
        )}
      </Stack>
    </Card>
  );
});

BasicInfo.displayName = 'BasicInfo';
export default BasicInfo;
