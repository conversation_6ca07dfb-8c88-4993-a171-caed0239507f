import { ResourceConfigTypeEnum } from '@common/constants/ResourceConfigTypeEnum';
import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class RancherApi extends BaseApi {
  static baseUrl = BaseUrl.ranchers;

  static findAllDeploymentByProjectId(projectId: string, resouceType: ResourceConfigTypeEnum, isFull: boolean = false) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/deployments/projects/${projectId}?configType=${resouceType}&isFull=${isFull}`);
  }

  static findAllNodes(resouceType: ResourceConfigTypeEnum, isFull: boolean = false) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/nodes?configType=${resouceType}&isFull=${isFull}`);
  }
  static findAllClusters(resouceType: ResourceConfigTypeEnum, isFull: boolean = false) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/clusters?configType=${resouceType}&isFull=${isFull}`);
  }
}
