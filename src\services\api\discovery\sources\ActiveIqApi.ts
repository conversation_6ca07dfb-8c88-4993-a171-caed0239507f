import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class ActiveIqApi extends BaseApi {
  static baseUrl = BaseUrl.activeIq;

  static findAllClusterNodes(isFull: boolean = false) {
    const params = { isFull };
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/data-center/clusters/nodes`, params);
  }

  static findAllStorageVolumes(isFull: boolean = false) {
    const params = { isFull };
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/data-center/storage/volumes`, params);
  }
}
