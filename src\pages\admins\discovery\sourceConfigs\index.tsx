import HeaderTitleComponent from '@components/HeaderTitleComponent';
import { KanbanTable, ColumnType, TableAffactedSafeType, KanbanTableProps, KanbanConfirmModal } from 'kanban-design-system';
import { IconPlus, IconEdit, IconTrash, IconEye } from '@tabler/icons-react';
import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { buildSourceDataConfigUrl } from '@common/utils/RouterUtils';
import { renderDateTime } from 'kanban-design-system';
import equal from 'fast-deep-equal';

import { KanbanText, KanbanButton, KanbanIconButton } from 'kanban-design-system';
import { isCurrentUserMatchPermissions } from '@common/utils/AclPermissionUtils';
import GuardComponent from '@components/GuardComponent';
import { SourceDataAction } from '@common/constants/SourceDataActionEnum';
import { NotificationSuccess } from '@common/utils/NotificationUtils';
import { BreadcrumbComponent } from '@pages/admins/breadcrumb/BreadcrumbComponent';
import { useDisclosure } from '@mantine/hooks';
import { tableAffectedToPaginationRequestModel } from '@common/utils/KanbanTableUtils';
import { DiscoverySourceResponse } from '@models/discovery/DiscoverySource';
import { DiscoverySourceApi } from '@api/discovery/DiscoverySourceApi';

export const SourcesPage = () => {
  const navigate = useNavigate();
  const [totalRecords, setTotalRecords] = useState(0);
  const [tableAffected, setTableAffected] = useState<TableAffactedSafeType | undefined>(undefined);
  const [listData, setListData] = useState<DiscoverySourceResponse[]>([]);
  const [modalConfirmDelete, { close: closeModalConfirmDelete, open: openModalConfirmDelete }] = useDisclosure(false);
  const [deleteIds, setDeleteIds] = useState<number[]>([]);

  const columns: ColumnType<DiscoverySourceResponse>[] = useMemo(() => {
    return [
      {
        title: 'Name',
        name: 'name',
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '20%',
      },
      {
        title: 'Description',
        name: 'description',
        customRender: (data) => {
          return (
            <KanbanText style={{ wordBreak: 'break-word' }} lineClamp={4}>
              {data}
            </KanbanText>
          );
        },
        width: '30%',
      },
      {
        title: 'Method',
        name: 'method',
      },
      {
        title: 'Type',
        name: 'type',
      },
      {
        title: 'Created By',
        name: 'createdBy',
      },
      {
        title: 'Last Discovery Status',
        name: 'lastDiscoveryStatus',
      },
      {
        title: 'lastDiscoveryTime',
        name: 'lastDiscoveryTime',
        customRender: (data) => {
          return data ? renderDateTime(data) : '';
        },
      },
    ];
  }, []);

  const getAllSourceDatas = useCallback(() => {
    if (!tableAffected) {
      return;
    }
    DiscoverySourceApi.getAll(tableAffectedToPaginationRequestModel(tableAffected))
      .then((res) => {
        if (res.data) {
          setListData(res.data?.content || []);
          setTotalRecords(res.data.totalElements);
        }
      })
      .catch(() => {});
    // setListData(mockDiscoveryConfigs);
    // setTotalRecords(mockDiscoveryConfigs.length);
  }, [tableAffected]);

  useEffect(() => {
    getAllSourceDatas();
  }, [getAllSourceDatas]);

  const deleteDataSources = useCallback(
    (ids: number[]) => {
      DiscoverySourceApi.deleteByIds(ids)
        .then(() => {
          NotificationSuccess({
            message: 'Deleted successfully',
          });
          // const newData = listData.filter((item) => !ids.includes(item.id));
          // setListData(newData);
          getAllSourceDatas();
        })
        .catch(() => {});
    },
    [getAllSourceDatas],
  );

  // const autoDiscovery = useCallback((id: number) => {
  //   DiscoveryStagingDataApi.discoverySourceByDiscoverySourceDataId(id)
  //     .then(() => {
  //       NotificationSuccess({ message: 'Auto discovery source success' });
  //     })
  //     .catch(() => {});
  // }, []);

  const tableViewListDataSourceProps: KanbanTableProps<DiscoverySourceResponse> = useMemo(() => {
    return {
      columns: columns,
      key: 1,
      data: listData,
      showNumericalOrderColumn: true,
      searchable: {
        enable: true,
        debounceTime: 300,
      },
      actions: {
        customAction: isCurrentUserMatchPermissions([])
          ? (data) => (
              <>
                {isCurrentUserMatchPermissions([]) && (
                  <KanbanIconButton
                    variant='transparent'
                    size='sm'
                    onClick={() => {
                      navigate(buildSourceDataConfigUrl(data.id, SourceDataAction.VIEW));
                    }}>
                    <IconEye />
                  </KanbanIconButton>
                )}
                {isCurrentUserMatchPermissions([]) && (
                  <KanbanIconButton
                    variant='transparent'
                    size='sm'
                    onClick={() => {
                      navigate(buildSourceDataConfigUrl(data.id, SourceDataAction.UPDATE));
                    }}>
                    <IconEdit />
                  </KanbanIconButton>
                )}
                {isCurrentUserMatchPermissions([]) && (
                  <KanbanIconButton
                    key={3}
                    title='Delete'
                    color='red'
                    size='sm'
                    variant='transparent'
                    onClick={() => {
                      setDeleteIds([data.id]);
                      openModalConfirmDelete();
                    }}>
                    <IconTrash />
                  </KanbanIconButton>
                )}
              </>
            )
          : undefined,
      },
      // selectableRows: {
      //   enable: !!isCurrentUserMatchPermissions([]),
      //   onDeleted: isCurrentUserMatchPermissions([]) ? (rows) => deleteDataSources(rows.map((x) => x.id)) : undefined,
      // },
      onRowClicked: (data) => {
        if (isCurrentUserMatchPermissions([])) {
          navigate(buildSourceDataConfigUrl(data.id, SourceDataAction.VIEW));
        }
      },
      pagination: {
        enable: true,
      },
      serverside: {
        totalRows: totalRecords,
        onTableAffected: (dataSet) => {
          if (!equal(tableAffected, dataSet)) {
            setTableAffected(dataSet);
          }
        },
      },
    };
  }, [columns, listData, navigate, openModalConfirmDelete, tableAffected, totalRecords]);

  return (
    <>
      {/* Modal confirm delete */}
      <KanbanConfirmModal
        opened={modalConfirmDelete}
        onClose={closeModalConfirmDelete}
        title='Confirm delete'
        onConfirm={() => {
          deleteDataSources(deleteIds);
          closeModalConfirmDelete();
        }}>
        Are you sure you want to delete this item?
      </KanbanConfirmModal>

      {/* 4763 Datasource list*/}
      <BreadcrumbComponent />
      <HeaderTitleComponent
        title='Source Config'
        rightSection={
          <GuardComponent requirePermissions={[]} hiddenOnUnSatisfy>
            <KanbanButton
              onClick={() => {
                navigate(buildSourceDataConfigUrl(0, SourceDataAction.CREATE));
              }}
              leftSection={<IconPlus />}>
              Add new
            </KanbanButton>
          </GuardComponent>
        }
      />
      <div style={{ flex: 2 }}>
        <KanbanTable {...tableViewListDataSourceProps} />
      </div>
    </>
  );
};
export default SourcesPage;
