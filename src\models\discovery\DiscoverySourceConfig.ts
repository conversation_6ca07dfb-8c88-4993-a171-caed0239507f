import {
  ApiType,
  AuthenticationType,
  DbType,
  LastDiscoveryStatusEnum,
  IntegrateMethodEnum as Method,
  SnmpType,
  SnmpTypeOid,
  SnmpVersion,
} from '@common/constants/DiscoverySourceConfigEnum';
import type { EntityModelBase } from '@models/EntityModelBase';
import { DiscoverySourceResponse } from './DiscoverySource';
import { ComboboxParsedItemGroup } from '@mantine/core';

export type DiscoverySourceConfigModel = EntityModelBase & {
  id: number;
  sourceId: number;
  tokenKey: string;
  usernameAuthen: string;
  passwordAuthen: string;
  limit: number;
};

export type DiscoverySourceConfigResponse = DiscoverySourceConfigModel;

export type Option = {
  name: string;
  value: string;
};

export type Param = Option & {
  type: string;
};
export type SnmpOidInfo = {
  type: SnmpTypeOid;
  oid: string;
  label: string;
};
export const defaultSnmpOidInfo: SnmpOidInfo = {
  type: SnmpTypeOid.SCALAR,
  oid: '',
  label: '',
};

export type SourceConfigBasicInfo = {
  name: string;
  method: Method | undefined;
  type: ApiType | DbType | SnmpType | undefined;
  description?: string;
  snmpVersion?: SnmpVersion;
  dbType: DbType;
};

export const defaultBasicInfo: SourceConfigBasicInfo = {
  name: '',
  method: undefined,
  type: undefined,
  snmpVersion: undefined,
  dbType: DbType.MSSQL,
};

export type SourceConfigApiInfo = {
  apiUrl?: string;
  authenticationType?: AuthenticationType;
  username?: string;
  password?: string;
  tokenKey?: string;
  params?: Param[];
};

export const defaultApiInfo: SourceConfigApiInfo = {
  apiUrl: '',
  authenticationType: undefined,
  username: '',
  password: '',
  tokenKey: '',
  params: [],
};

export type SourceConfigDatabaseInfo = {
  server?: string;
  port?: string;
  databaseName?: string;
  username?: string;
  password?: string;
  // extraOptions?: Option[];
};

export const defaultDatabaseInfo: SourceConfigDatabaseInfo = {
  server: '',
  port: '',
  databaseName: '',
  username: '',
  password: '',
  // extraOptions: [],
};
export type SourceConfigSnmpInfo = {
  id: string;
  hostName: string;
  version?: SnmpType;
  username?: string;
  password?: string;
  community?: string;
  authentication?: string;
  encryption?: string;
  encryptionKey?: string;
  oidsInfo: SnmpOidInfo[];
};

export const defaultSnmpInfo: SourceConfigSnmpInfo = {
  id: '',
  hostName: '',
  version: undefined,
  username: undefined,
  password: '',
  community: '',
  authentication: undefined,
  encryption: undefined,
  encryptionKey: '',
  oidsInfo: [defaultSnmpOidInfo],
};
export type DiscoverySourceConfigDetail = {
  basicInfo: SourceConfigBasicInfo;
  apiInfo?: SourceConfigApiInfo;
  databaseInfo?: SourceConfigDatabaseInfo;
  snmpInfos?: SourceConfigSnmpInfo[];
};

export const defaultDiscoverySourceConfigDetail: DiscoverySourceConfigDetail = {
  basicInfo: defaultBasicInfo,
};

export const discoverySourceConfigDetailDefault: DiscoverySourceResponse = {
  id: 0,
  name: '',
  method: Method.API,
  type: undefined,
  lastDiscoveryStatus: LastDiscoveryStatusEnum.SUCCESS,
  lastDiscoveryTime: new Date(),
  dbType: DbType.MSSQL,
};

export const rawDataSource: Record<ApiType, string[]> = {
  [ApiType.RANCHER]: ['K8s microservice application', 'Tanzu cluster node', 'Tanzu clusters'],
  [ApiType.NETBOX]: ['Netbox virtual machine', 'Netbox virtual disk', ' Netbox devices', 'Netbox virtual cluster', 'Netbox devices (role=sever)'],
  [ApiType.HMC]: ['Aix hmc logical partition'],
  [ApiType.VROPS]: ['Vrops Clusters', 'Vrop Resources'],
  [ApiType.ACTIVEIQ]: ['ActiveIq volumes'],
};

export const sourceCombobox: ComboboxParsedItemGroup[] = Object.entries(rawDataSource).map(([key, items]) => ({
  group: key,
  items: items.map((item) => ({
    label: item,
    value: item,
  })),
}));
