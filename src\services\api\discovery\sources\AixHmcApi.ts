import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class AixHmcApi extends BaseApi {
  static baseUrl = BaseUrl.aixHmcs;

  static findAllDataAixHmc(isFull: boolean = false) {
    const params = { isFull };
    return BaseApi.getData<JSONNode>(`${this.baseUrl}`, params);
  }

  static findAllManagedSystem(isFull: boolean = false) {
    const params = { isFull };
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/managed-system`, params);
  }

  static findAllLogicalPartition(isFull: boolean = false) {
    const params = { isFull };
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/logical-partition`, params);
  }
}
