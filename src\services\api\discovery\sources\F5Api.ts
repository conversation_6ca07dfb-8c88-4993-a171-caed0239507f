import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class F5Api extends BaseApi {
  static baseUrl = BaseUrl.f5s;

  static findAllDevice(isFull: boolean = false) {
    const params = { isFull };
    return BaseApi.getData<JSONNode>(`${this.baseUrl}`, params);
  }
}
