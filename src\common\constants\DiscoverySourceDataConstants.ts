import { ComboboxItem } from '@mantine/core';
import { DiscoverySourceTypeEnum } from './DiscoverySourceTypeEnum';
// TODO: fetch data

export type SourceData = {
  data: string;
  type: DiscoverySourceTypeEnum;
};

export const SOURCES = new Map<string, SourceData>([
  ['1', { data: 'c-ddtpn:p-7q7bp', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_K8S_PROJECT }],
  // ['2', { data: 'c-ddtpn:p-6xc6f', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_K8S_PROJECT }],
  ['3', { data: '116', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_MACHINE }],
  ['4', { data: '104', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_DISK }],
  ['5', { data: 'local:p-x649f', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU }],
  ['6', { data: '001', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_AIX_HMC }],
  ['7', { data: '102', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_DICM_DEVICE }],
  ['8', { data: '001', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_VIRTUALIZATION_VIRTUAL_CLUSTER }],
  ['9', { data: '001', type: DiscoverySourceTypeEnum.DISCOVERY_SOURCE_AIX_HMC_MANAGED_SYSTEM }],
  ['10', { data: '001', type: DiscoverySourceTypeEnum.DISCOVERY_SOURCE_AIX_HMC_LOGICAL_PARTITION }],
  ['11', { data: '001', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_VROPS_CLUSTER }],
  ['12', { data: '001', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_VROPS_RESOURCES }],
  ['13', { data: '001', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_SOLARWIND_NETWORK_USE_API }],
  ['14', { data: '001', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU_NODE }],
  ['15', { data: '001', type: DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_NETWORK_CHECKPOINT }],
  ['16', { data: '001', type: DiscoverySourceTypeEnum.DISCOVERY_SOURCE_SNMP_NETWORK_PALOALTO }],
  ['17', { data: '001', type: DiscoverySourceTypeEnum.DISCOVERY_SOURCE_ACTIVE_IQ_CLUSTER_NODE }],
  ['18', { data: '001', type: DiscoverySourceTypeEnum.DISCOVERY_SOURCE_ACTIVE_IQ_STORAGE_VOLUMES }],
  ['19', { data: '001', type: DiscoverySourceTypeEnum.DISCOVERY_SOURCE_F5_SNMP }],
  ['20', { data: '001', type: DiscoverySourceTypeEnum.DISCOVERY_SOURCE_F5_API }],
  ['21', { data: '001', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_SOLARWIND_NETWORK_USE_DB }],
  ['22', { data: '001', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_RANCHER_TANZU_CLUSTERS }],
  ['23', { data: '001', type: DiscoverySourceTypeEnum.DISCOVER_SOURCE_NETBOX_DICM_DEVICE_ROLE_SERVER }],
]);

// TODO: Fetch all source need discovery
export const SOURCE_DISCOVERIES_COMBOBOX: ComboboxItem[] = [
  { label: 'k8s', value: '1' },
  // { label: 'k8s-dev-card_deployments', value: '2' },
  { label: 'netbox-virtual_machine', value: '3' },
  { label: 'netbox-virtual_disk', value: '4' },
  // { label: 'rancher-tanzu_nginx-deployment', value: '5' },
  { label: 'aix_hmc_datas', value: '6' },
  { label: 'netbox-devices', value: '7' },
  { label: 'netbox-virtual_cluster', value: '8' },
  { label: 'aix_hmc_managed_system', value: '9' },
  { label: 'aix_hmc_logical_partition', value: '10' },
  { label: 'vrops-clusters', value: '11' },
  { label: 'vrops-resources', value: '12' },
  { label: 'solarwinds_devices_cisco (use api)', value: '13' },
  { label: 'rancher-tanzu-node', value: '14' },
  { label: 'snmp-security_checkpoint', value: '15' },
  { label: 'snmp-security_paloalto', value: '16' },
  { label: 'active_iq_cluster_node', value: '17' },
  { label: 'active_iq_storage_volumes', value: '18' },
  { label: 'F5_SNMP', value: '19' },
  { label: 'F5_API', value: '20' },
  { label: 'solarwinds_devices_cisco(use db)', value: '21' },
  { label: 'rancher-tanzu-clusters', value: '22' },
  { label: 'netbox-device-role-server', value: '23' },
];

// TODO: Fetch all source need discovery
export const SOURCE_DISCOVERIES_LIVE_COMBOBOX: ComboboxItem[] = [
  { label: 'netbox-virtual_machine', value: '3' },
  { label: 'netbox-virtual_disk', value: '4' },
  // { label: 'rancher-tanzu_nginx-deployment', value: '5' },
  { label: 'netbox-device', value: '7' },
  { label: 'netbox-device-role-server', value: '23' },
  { label: 'netbox-virtual_cluster', value: '8' },
  { label: 'vrops-clusters', value: '11' },
  { label: 'vrops-resources', value: '12' },
  // { label: 'solarwind-network', value: '13' },
  { label: 'rancher-tanzu-node', value: '14' },
  { label: 'rancher-tanzu-clusters', value: '22' },
  { label: 'snmp-security_checkpoint', value: '15' },
  { label: 'snmp-security_paloalto', value: '16' },
  { label: 'solarwinds_devices_cisco', value: '21' },
  { label: 'F5_SNMP', value: '19' },
  { label: 'aix_hmc_logical_partition', value: '10' },
  { label: 'active_iq_storage_volumes', value: '18' },
];
