import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class VropsApi extends BaseApi {
  static baseUrl = BaseUrl.vrops;

  static findAllClusters(isFull: boolean = false) {
    const params = { isFull };
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/clusters`, params);
  }

  static findAllResources(isFull: boolean = false) {
    const params = { isFull };
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/resources`, params);
  }
}
