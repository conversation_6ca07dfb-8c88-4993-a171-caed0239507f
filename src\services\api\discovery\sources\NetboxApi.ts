import { DeviceRoleEnum } from '@common/constants/DeviceRoleEnum';
import { BaseApi } from '@core/api/BaseApi';
import { BaseUrl } from '@core/api/BaseUrl';
import { JSONNode } from '@pages/admins/discovery/jsonTransform/helper/JsonTransformHelper';

export class Netbox<PERSON>pi extends BaseApi {
  static baseUrl = BaseUrl.netboxs;

  static findVirtualMachineById(id: string) {
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/virtual-machines/${id}`);
  }

  static findVirtualMachines(isFull: boolean = false) {
    const params = { isFull };
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/virtual-machines`, params);
  }

  static findVirtualDisks(isFull: boolean = false) {
    const params = { isFull };
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/virtual-disks`, params);
  }

  static findVirtualClusters(isFull: boolean = false) {
    const params = { isFull };
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/clusters`, params);
  }

  static findDevices(role?: DeviceRoleEnum, isFull: boolean = false) {
    const params: any = { isFull };
    if (role) {
      params.role = role;
    }
    return BaseApi.getData<JSONNode>(`${this.baseUrl}/devices`, params);
  }
}
