export enum IntegrateMethodEnum {
  API = 'API',
  DB_CONNECT = 'DB_CONNECT',
  SNMP = 'SNMP',
}

export type IntegrateMethodEnumKey = keyof typeof IntegrateMethodEnum;

export enum ApiType {
  NETBOX = 'NETBOX',
  VROPS = 'VROPS',
  ACTIVEIQ = 'ACTIVEIQ',
  RANCHER = 'RANCHER',
  HMC = 'HMC',
}
// export type ApiTypeKey = keyof typeof ApiType;

export enum DbType {
  MSSQL = 'MSSQL',
}

export enum SourceDbType {
  SOLARWIND = 'Solarwind',
}

// export type DbTypeKey = keyof typeof DbType;

export enum SnmpType {
  CHECK_POINT = 'Check point',
  PALO_ALTO = 'Paloalto',
  F5 = 'F5',
}
export enum SnmpVersion {
  SNMPV2 = 'SNMPV2',
  SNMPV3 = 'SNMPV3',
}
export enum SnmpTypeOid {
  SCALAR = 'Scalar',
  TABLE = 'Table  ',
}

export enum SnmpAuthentication {
  MD5 = 'MD5',
  SHA1 = 'SHA1',
  SHA256 = 'SHA256',
}

export enum SnmpEncryption {
  DES = 'DES',
  AES128 = 'AES128',
}

export type SnmpVersionKey = keyof typeof SnmpVersion;
// export type SnmpTypeKey = keyof typeof SnmpType;

export enum AuthenticationType {
  BASIC = 'BASIC',
  API_TOKEN = 'API_TOKEN',
}
export type AuthenticationTypeKey = keyof typeof AuthenticationType;

export enum LastDiscoveryStatusEnum {
  SUCCESS = 'Success',
  CONNECT_ERROR = 'Connect Error',
}

export enum ParamType {
  URL = 'URL',
  HEADER = 'HEADER',
}
