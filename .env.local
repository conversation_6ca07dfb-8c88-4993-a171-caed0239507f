GENERATE_SOURCEMAP=true
REACT_APP_NAME=CMDB
REACT_APP_FULLNAME=ITCMDB
REACT_APP_SHOW_DEVELOPMENT_GUIDE=true
REACT_APP_DESCRIPTION="IT Configuration Management Database"
REACT_APP_DEPLOY_URL=http://localhost:9990
REACT_APP_ITCMDB_API_URL=
######Config for remote url service
REACT_APP_ITCMDB_API_URL_REMOTE=https://itcmdb.tanzu-uat.mbbank.com.vn
#Split by ",", ex: server,discovery
REACT_APP_ITCMDB_API_SERVICE_REMOTE=server
######End Config for remote url service

#Keycloak config
REACT_APP_KEYCLOAK_URL=https://keycloak-internal-uat.mbbank.com.vn/auth
REACT_APP_KEYCLOAK_REALM=internal
REACT_APP_KEYCLOAK_CLIENT_ID=itcmdb-frontend
REACT_APP_KEYCLOAK_ENABLED=true


#Feature
# default true (show feature), set key === false -> disable feature
REACT_APP_FEATURE_CMDB_DEMO=true
REACT_APP_FEATURE_CI_TYPE_REFERENCE_ATTRIBUTE=true
REACT_APP_FEATURE_CHANGE_IMPACTED_SERVICE=false
REACT_APP_FEATURE_DISCOVERY_SOURCE_DATA=true